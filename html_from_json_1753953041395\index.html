<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信公众号文章列表</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .stats {
            color: #666;
            font-size: 14px;
        }
        .article-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        .article-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .article-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        .article-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .article-title a {
            text-decoration: none;
            color: inherit;
        }
        .article-title a:hover {
            color: #3498db;
        }
        .article-meta {
            color: #666;
            font-size: 12px;
            margin-bottom: 10px;
        }
        .article-digest {
            color: #7f8c8d;
            font-size: 14px;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        @media (max-width: 768px) {
            .article-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📚 微信公众号文章列表</h1>
        <div class="stats">
            共 2 篇文章 | 生成时间: 2025/7/31 17:10:41
        </div>
    </div>
    
    <div class="article-grid">
        
                <div class="article-card">
                    <div class="article-title">
                        <a href="RROaRu5xmCk2TyUW0i5bixxS-TK-NK5MeSQ-WyJf1xilY43EEUumj644BnrRcH5o_1_【重磅新品】贝启RK3576核心板_开发板.html">【重磅新品】贝启RK3576核心板/开发板</a>
                    </div>
                    <div class="article-meta">
                        👤 Bearkey | 🆔 RROaRu5xmCk2TyUW0i5bixxS-TK-NK5MeSQ-WyJf1xilY43EEUumj644BnrRcH5o
                    </div>
                    <div class="article-digest">贝启RK3576开发板采用瑞芯微第一款单面贴，小而薄的模组</div>
                </div>
                <div class="article-card">
                    <div class="article-title">
                        <a href="RROaRu5xmCk2TyUW0i5bi61X1e05XZl18tsOTVs9VOlBk9J-wtZhVGaBJeoGtKp2_1_新起点、新辉煌——热烈祝贺公司乔迁大吉！.html">新起点、新辉煌——热烈祝贺公司乔迁大吉！</a>
                    </div>
                    <div class="article-meta">
                        👤 未知 | 🆔 RROaRu5xmCk2TyUW0i5bi61X1e05XZl18tsOTVs9VOlBk9J-wtZhVGaBJeoGtKp2
                    </div>
                    <div class="article-digest">一、乔迁盛典2023年8月26日上午十点整，厦门贝启迎来八周年发展历程中的又一个重要里程碑——正式入驻星网锐</div>
                </div>
    </div>
</body>
</html>