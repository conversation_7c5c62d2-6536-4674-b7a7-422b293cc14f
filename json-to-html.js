const fs = require('fs');
const path = require('path');

/**
 * 从JSON文件生成HTML文件
 * @param {string} jsonFilePath JSON文件路径
 * @param {string} outputDir 输出目录
 */
function convertJsonToHtml(jsonFilePath, outputDir = './html_from_json') {
    try {
        // 读取JSON文件
        console.log(`📖 正在读取JSON文件: ${jsonFilePath}`);
        const jsonData = JSON.parse(fs.readFileSync(jsonFilePath, 'utf8'));
        
        // 确保输出目录存在
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
            console.log(`📁 创建输出目录: ${outputDir}`);
        }
        
        let totalFiles = 0;
        let successCount = 0;
        const failedArticles = [];
        
        console.log(`📄 开始处理 ${jsonData.length} 篇文章...`);
        
        jsonData.forEach((article, index) => {
            try {
                console.log(`\n[${index + 1}/${jsonData.length}] 处理文章: ${article.article_id}`);
                
                if (article.news_item && article.news_item.length > 0) {
                    article.news_item.forEach((item, itemIndex) => {
                        const htmlContent = generateSingleArticleHTML(article, item, itemIndex);
                        
                        // 生成安全的文件名
                        const safeTitle = (item.title || `article_${itemIndex + 1}`)
                            .replace(/[<>:"/\\|?*]/g, '_')
                            .replace(/\s+/g, '_')
                            .substring(0, 50);
                        
                        const filename = `${article.article_id}_${itemIndex + 1}_${safeTitle}.html`;
                        const filepath = path.join(outputDir, filename);
                        
                        fs.writeFileSync(filepath, htmlContent, 'utf8');
                        totalFiles++;
                        
                        console.log(`  ✅ 生成: ${filename}`);
                    });
                    successCount++;
                } else {
                    console.log(`  ⚠️  文章 ${article.article_id} 没有内容`);
                }
                
            } catch (error) {
                console.error(`  ❌ 处理文章 ${article.article_id} 失败: ${error.message}`);
                failedArticles.push({
                    article_id: article.article_id,
                    error: error.message
                });
            }
        });
        
        // 生成索引页面
        generateIndexPage(jsonData, outputDir);
        
        // 显示统计信息
        console.log('\n' + '='.repeat(60));
        console.log('📊 转换完成统计');
        console.log('='.repeat(60));
        console.log(`处理文章数: ${jsonData.length}`);
        console.log(`成功转换: ${successCount}`);
        console.log(`失败数量: ${failedArticles.length}`);
        console.log(`HTML文件数: ${totalFiles}`);
        console.log(`输出目录: ${outputDir}`);
        console.log(`成功率: ${((successCount / jsonData.length) * 100).toFixed(1)}%`);
        console.log('='.repeat(60));
        
        if (failedArticles.length > 0) {
            console.log('\n❌ 失败的文章:');
            failedArticles.forEach(item => {
                console.log(`  - ${item.article_id}: ${item.error}`);
            });
        }
        
        console.log(`\n🎉 转换完成！请查看目录: ${outputDir}`);
        console.log(`📋 索引页面: ${path.join(outputDir, 'index.html')}`);
        
    } catch (error) {
        console.error(`❌ 转换失败: ${error.message}`);
        throw error;
    }
}

/**
 * 清理微信公众号HTML内容
 * @param {string} content 原始HTML内容
 * @returns {string} 清理后的HTML内容
 */
function cleanWechatContent(content) {
    if (!content) return '<p>暂无内容</p>';

    let cleanedContent = content;

    // 移除微信特有的属性
    cleanedContent = cleanedContent.replace(/\s*data-[^=]*="[^"]*"/g, '');
    cleanedContent = cleanedContent.replace(/\s*uuid="[^"]*"/g, '');
    cleanedContent = cleanedContent.replace(/\s*title="[^"]*"/g, '');
    cleanedContent = cleanedContent.replace(/\s*ignoreparse="[^"]*"/g, '');
    cleanedContent = cleanedContent.replace(/\s*styleid="[^"]*"/g, '');

    // 简化复杂的section结构，保留基本的段落和图片
    cleanedContent = cleanedContent.replace(/<section[^>]*>/g, '<div>');
    cleanedContent = cleanedContent.replace(/<\/section>/g, '</div>');

    // 清理多余的div嵌套
    cleanedContent = cleanedContent.replace(/<div>\s*<div>/g, '<div>');
    cleanedContent = cleanedContent.replace(/<\/div>\s*<\/div>/g, '</div>');

    // 保留图片但简化属性
    cleanedContent = cleanedContent.replace(/<img[^>]*class="rich_pages wxw-img"[^>]*data-src="([^"]*)"[^>]*>/g,
        '<img src="$1" style="max-width: 100%; height: auto; display: block; margin: 20px auto;" alt="图片">');

    // 清理空的div
    cleanedContent = cleanedContent.replace(/<div[^>]*>\s*<\/div>/g, '');

    // 清理多余的空白
    cleanedContent = cleanedContent.replace(/\s+/g, ' ');
    cleanedContent = cleanedContent.replace(/>\s+</g, '><');

    return cleanedContent;
}

/**
 * 生成单篇文章的HTML内容
 * @param {Object} article 文章对象
 * @param {Object} item 文章项
 * @param {number} itemIndex 文章项索引
 * @returns {string} HTML内容
 */
function generateSingleArticleHTML(article, item, itemIndex) {
    const cleanedContent = cleanWechatContent(item.content);

    return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${item.title || '微信公众号文章'}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 100%;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .article-container {
            max-width: 800px;
            margin: 20px auto;
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .article-header {
            border-bottom: 2px solid #eee;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .article-title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 15px;
            line-height: 1.4;
            color: #2c3e50;
        }
        .article-meta {
            color: #666;
            font-size: 14px;
            margin-bottom: 10px;
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .article-digest {
            color: #7f8c8d;
            font-style: italic;
            margin-bottom: 15px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            border-radius: 4px;
        }
        .article-content {
            font-size: 16px;
            line-height: 1.8;
            word-wrap: break-word;
        }
        .article-content img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 20px auto;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .article-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 12px;
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 4px;
        }
        .back-link {
            display: inline-block;
            margin-bottom: 20px;
            color: #3498db;
            text-decoration: none;
            font-size: 14px;
        }
        .back-link:hover {
            text-decoration: underline;
        }
        /* 清理后的内容样式 */
        .article-content div {
            margin: 10px 0;
        }
        .article-content p {
            margin: 15px 0;
            line-height: 1.8;
        }
        .article-content blockquote {
            margin: 20px 0;
            padding: 15px 20px;
            background-color: #f8f9fa;
            border-left: 4px solid #ddd;
            border-radius: 4px;
        }
        .article-content h1, .article-content h2, .article-content h3 {
            margin: 25px 0 15px 0;
            color: #2c3e50;
        }
        .article-content span {
            line-height: inherit;
        }
        /* 隐藏可能的错误元素 */
        .article-content [style*="display:none"] {
            display: none !important;
        }
        /* 响应式设计 */
        @media (max-width: 768px) {
            .article-container {
                margin: 10px;
                padding: 20px;
            }
            .article-title {
                font-size: 22px;
            }
            .article-content {
                font-size: 15px;
            }
            .article-meta {
                flex-direction: column;
                gap: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="article-container">
        <a href="index.html" class="back-link">← 返回文章列表</a>
        
        <div class="article-header">
            <h1 class="article-title">${item.title || '无标题'}</h1>
            <div class="article-meta">
                <span>👤 作者: ${item.author || '未知'}</span>
                <span>🆔 文章ID: ${article.article_id}</span>
                ${item.content_source_url ? `<span>🔗 <a href="${item.content_source_url}" target="_blank">原文链接</a></span>` : ''}
            </div>
            ${item.digest ? `<div class="article-digest">📝 ${item.digest}</div>` : ''}
        </div>
        
        <div class="article-content">
            ${cleanedContent}
        </div>
        
        <div class="article-footer">
            <p><strong>文章信息:</strong></p>
            <p>• 文章ID: ${article.article_id}</p>
            <p>• 生成时间: ${new Date().toLocaleString('zh-CN')}</p>
            ${item.thumb_url ? `<p>• 封面图片: <a href="${item.thumb_url}" target="_blank">查看原图</a></p>` : ''}
            <p>• 评论设置: ${item.need_open_comment ? '开启评论' : '关闭评论'}</p>
            ${item.url ? `<p>• 临时链接: <a href="${item.url}" target="_blank">查看</a></p>` : ''}
        </div>
    </div>
</body>
</html>`;
}

/**
 * 生成索引页面
 * @param {Array} articles 文章数组
 * @param {string} outputDir 输出目录
 */
function generateIndexPage(articles, outputDir) {
    const indexContent = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信公众号文章列表</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei UI", "Microsoft YaHei", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        .stats {
            color: #666;
            font-size: 14px;
        }
        .article-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
            gap: 20px;
        }
        .article-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .article-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
        }
        .article-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        .article-title a {
            text-decoration: none;
            color: inherit;
        }
        .article-title a:hover {
            color: #3498db;
        }
        .article-meta {
            color: #666;
            font-size: 12px;
            margin-bottom: 10px;
        }
        .article-digest {
            color: #7f8c8d;
            font-size: 14px;
            line-height: 1.5;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        @media (max-width: 768px) {
            .article-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>📚 微信公众号文章列表</h1>
        <div class="stats">
            共 ${articles.length} 篇文章 | 生成时间: ${new Date().toLocaleString('zh-CN')}
        </div>
    </div>
    
    <div class="article-grid">
        ${articles.map((article, index) => {
            if (!article.news_item || article.news_item.length === 0) return '';
            
            return article.news_item.map((item, itemIndex) => {
                const safeTitle = (item.title || `article_${itemIndex + 1}`)
                    .replace(/[<>:"/\\|?*]/g, '_')
                    .replace(/\s+/g, '_')
                    .substring(0, 50);
                const filename = `${article.article_id}_${itemIndex + 1}_${safeTitle}.html`;
                
                return `
                <div class="article-card">
                    <div class="article-title">
                        <a href="${filename}">${item.title || '无标题'}</a>
                    </div>
                    <div class="article-meta">
                        👤 ${item.author || '未知'} | 🆔 ${article.article_id}
                    </div>
                    ${item.digest ? `<div class="article-digest">${item.digest}</div>` : ''}
                </div>`;
            }).join('');
        }).join('')}
    </div>
</body>
</html>`;

    const indexPath = path.join(outputDir, 'index.html');
    fs.writeFileSync(indexPath, indexContent, 'utf8');
    console.log(`📋 生成索引页面: index.html`);
}

// 如果直接运行此文件
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.length === 0) {
        console.log('使用方法: node json-to-html.js <JSON文件路径> [输出目录]');
        console.log('示例: node json-to-html.js all_articles_1753266656246.json ./html_output');
        process.exit(1);
    }
    
    const jsonFilePath = args[0];
    const outputDir = args[1] || `./html_from_json_${Date.now()}`;
    
    if (!fs.existsSync(jsonFilePath)) {
        console.error(`❌ JSON文件不存在: ${jsonFilePath}`);
        process.exit(1);
    }
    
    console.log('🚀 开始将JSON转换为HTML文件...');
    console.log(`📖 输入文件: ${jsonFilePath}`);
    console.log(`📁 输出目录: ${outputDir}`);
    console.log('');
    
    try {
        convertJsonToHtml(jsonFilePath, outputDir);
    } catch (error) {
        console.error(`❌ 转换失败: ${error.message}`);
        process.exit(1);
    }
}

module.exports = {
    convertJsonToHtml,
    generateSingleArticleHTML,
    generateIndexPage
};
